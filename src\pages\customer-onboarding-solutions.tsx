import React, { useEffect } from "react";
import { navigate } from "gatsby";

const CustomerOnboardingSolutionsRedirect: React.FC = () => {
    useEffect(() => {
        // Redirect to the correct page
        navigate("/customer-onboarding/", { replace: true });
    }, []);

    // Show loading while redirecting
    return (
        <div className="min-h-screen flex flex-col justify-center items-center">
            <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p>Redirecting...</p>
            </div>
        </div>
    );
};

export default CustomerOnboardingSolutionsRedirect;
